#!/usr/bin/env python3
"""
Test script for new functionality in desaka_unifier.

This script tests:
1. Memory file optimization
2. Product combination functionality
3. Export manager functionality
"""

import os
import sys
import logging
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from shared.logging_config import setup_logging
from unifierlib.memory_manager import load_frequently_used_memory_files, load_all_memory_files
from unifierlib.product_combiner import ProductCombiner
from unifierlib.export_manager import ExportManager
from unifierlib.export_product import ExportMainProduct, ExportProductVariant


def test_memory_optimization():
    """Test memory file optimization."""
    print("Testing memory file optimization...")
    
    # Test directory
    memory_dir = os.path.join(os.path.dirname(__file__), "Memory")
    language = "CS"
    
    if not os.path.exists(memory_dir):
        print(f"Memory directory not found: {memory_dir}")
        return False
    
    try:
        # Test frequent memory loading
        frequent_data = load_frequently_used_memory_files(memory_dir, language)
        print(f"Loaded {len(frequent_data)} frequent memory files")
        
        # Test all memory loading
        all_data = load_all_memory_files(memory_dir, language)
        print(f"Loaded {len(all_data)} total memory files")
        
        print("✓ Memory optimization test passed")
        return True
        
    except Exception as e:
        print(f"✗ Memory optimization test failed: {str(e)}")
        return False


def test_product_combination():
    """Test product combination functionality."""
    print("Testing product combination...")
    
    try:
        # Create test export directory
        test_export_dir = os.path.join(os.path.dirname(__file__), "test_exports")
        os.makedirs(test_export_dir, exist_ok=True)
        
        # Create test products
        json_products = []
        repaired_products = []
        
        # JSON product (from pincesobchod)
        json_product = ExportMainProduct()
        json_product.nazev = "Test Product 1"
        json_product.kod = "TEST001"
        json_product.cena = 100.0
        json_product.vyrobce = "Test Brand"
        json_products.append(json_product)
        
        # Repaired product (same name, different code/price)
        repaired_product = ExportMainProduct()
        repaired_product.nazev = "Test Product 1"
        repaired_product.kod = "TEST001-NEW"
        repaired_product.cena = 120.0
        repaired_product.vyrobce = "Test Brand"
        repaired_products.append(repaired_product)
        
        # New product (only in repaired)
        new_product = ExportMainProduct()
        new_product.nazev = "New Product"
        new_product.kod = "NEW001"
        new_product.cena = 50.0
        new_product.vyrobce = "New Brand"
        repaired_products.append(new_product)
        
        # Initialize combiner
        combiner = ProductCombiner(test_export_dir)
        
        # Test combination
        combined_products, reports = combiner.combine_products(json_products, repaired_products)
        
        print(f"Combined products: {len(combined_products)}")
        print(f"New products: {len(reports['new_products'])}")
        print(f"Code changes: {len(reports['code_changes'])}")
        print(f"Price increases: {len(reports['price_increases'])}")

        # Check if Czech filenames were created
        results_dir = os.path.join(test_export_dir, "Výsledky")
        if os.path.exists(results_dir):
            files = os.listdir(results_dir)
            czech_files = [f for f in files if any(czech in f for czech in ['nove_produkty', 'zmeny_kodu', 'zdrazeni'])]
            print(f"Czech report files created: {len(czech_files)}")
        
        print("✓ Product combination test passed")
        return True
        
    except Exception as e:
        print(f"✗ Product combination test failed: {str(e)}")
        return False


def test_export_manager():
    """Test export manager functionality."""
    print("Testing export manager...")
    
    try:
        # Create test export directory
        test_export_dir = os.path.join(os.path.dirname(__file__), "test_exports")
        os.makedirs(test_export_dir, exist_ok=True)
        
        # Create test products
        test_products = []
        
        # Main product
        main_product = ExportMainProduct()
        main_product.nazev = "Export Test Product"
        main_product.kod = "EXP001"
        main_product.cena = 75.0
        main_product.vyrobce = "Export Brand"
        main_product.typ = "produkt"
        main_product.kategorie_id = "Sports/Table Tennis/Rackets"
        test_products.append(main_product)
        
        # Variant product
        variant_product = ExportProductVariant()
        variant_product.nazev = "Export Test Product"
        variant_product.kod = "EXP001-V01"
        variant_product.cena = 75.0
        variant_product.vyrobce = "Export Brand"
        variant_product.typ = "varianta"
        variant_product.kategorie_id = "Sports/Table Tennis/Rackets"
        variant_product.varianta1_nazev = "Color"
        variant_product.varianta1_hodnota = "Red"
        test_products.append(variant_product)
        
        # Initialize export manager
        export_manager = ExportManager(test_export_dir)
        
        # Test new products data
        new_products_data = [{"kod": "EXP001", "nazev": "Export Test Product"}]
        
        # Test comprehensive export
        created_files = export_manager.export_comprehensive_reports(test_products, new_products_data)
        
        print(f"Created files: {len(created_files)}")
        for file_type, file_path in created_files.items():
            print(f"  - {file_type}: {os.path.basename(file_path)}")

        # Check if Czech directory names were created
        reports_dirs = [d for d in os.listdir(test_export_dir) if d.startswith("Reporty_")]
        if reports_dirs:
            reports_dir = os.path.join(test_export_dir, reports_dirs[0])
            if os.path.exists(os.path.join(reports_dir, "Detailni_reporty")):
                detail_dir = os.path.join(reports_dir, "Detailni_reporty")
                subdirs = os.listdir(detail_dir)
                czech_dirs = [d for d in subdirs if any(czech in d for czech in ['Podle_', 'Typ_', 'Znacka_'])]
                print(f"Czech subdirectories created: {len(czech_dirs)}")

        print("✓ Export manager test passed")
        return True
        
    except Exception as e:
        print(f"✗ Export manager test failed: {str(e)}")
        return False


def main():
    """Run all tests."""
    print("=" * 60)
    print("TESTING NEW FUNCTIONALITY")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(message)s')
    
    # Run tests
    tests = [
        test_memory_optimization,
        test_product_combination,
        test_export_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
            print()
        except Exception as e:
            print(f"✗ Test {test_func.__name__} crashed: {str(e)}")
            print()
    
    print("=" * 60)
    print(f"TEST RESULTS: {passed}/{total} tests passed")
    print("=" * 60)
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    sys.exit(main())
